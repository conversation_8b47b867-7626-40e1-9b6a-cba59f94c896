#!/usr/bin/env python3
"""
Enhanced MCP Pipeline Startup Script
Easy-to-use startup script for the automated web scraping and content generation pipeline.
"""

import os
import sys
import subprocess
import time
import signal
import threading
from pathlib import Path
import json


class PipelineStarter:
    """Startup manager for the enhanced pipeline system"""
    
    def __init__(self):
        self.processes = {}
        self.running = True
        
        # Setup signal handlers
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)

    def _signal_handler(self, signum, frame):
        """Handle shutdown signals"""
        print(f"\n🛑 Received signal {signum}, shutting down...")
        self.running = False
        self._stop_all_processes()

    def _stop_all_processes(self):
        """Stop all running processes"""
        for name, process in self.processes.items():
            if process and process.poll() is None:
                print(f"Stopping {name}...")
                process.terminate()
                try:
                    process.wait(timeout=10)
                except subprocess.TimeoutExpired:
                    process.kill()

    def check_dependencies(self) -> bool:
        """Check if all dependencies are available"""
        print("🔍 Checking dependencies...")
        
        # Check Python version
        if sys.version_info < (3, 8):
            print("❌ Python 3.8+ required")
            return False
        
        # Check required packages
        required_packages = [
            'redis', 'pandas', 'flask', 'requests', 'beautifulsoup4', 
            'schedule', 'aioredis'
        ]
        
        missing_packages = []
        for package in required_packages:
            try:
                __import__(package)
            except ImportError:
                missing_packages.append(package)
        
        if missing_packages:
            print(f"❌ Missing packages: {', '.join(missing_packages)}")
            print("Install with: pip install -r requirements.txt")
            return False
        
        print("✅ All dependencies satisfied")
        return True

    def check_redis(self) -> bool:
        """Check if Redis is available"""
        try:
            import redis
            client = redis.Redis(host='localhost', port=6379, db=0)
            client.ping()
            print("✅ Redis server is running")
            return True
        except Exception:
            print("⚠️  Redis server not available - will use CSV-only mode")
            return False

    def setup_directories(self):
        """Create necessary directories"""
        print("📁 Setting up directories...")
        
        directories = ['data', 'logs', 'config', 'templates']
        for directory in directories:
            Path(directory).mkdir(exist_ok=True)
        
        print("✅ Directories ready")

    def start_admin_panel(self) -> bool:
        """Start the admin panel"""
        try:
            print("🌐 Starting admin panel...")
            
            admin_script = Path("pipeline/admin/enhanced_admin_panel.py")
            if not admin_script.exists():
                print("❌ Admin panel script not found")
                return False
            
            process = subprocess.Popen([
                sys.executable, str(admin_script)
            ], stdout=subprocess.PIPE, stderr=subprocess.PIPE)
            
            self.processes['admin_panel'] = process
            
            # Wait a moment to check if it started successfully
            time.sleep(2)
            if process.poll() is None:
                print("✅ Admin panel started on http://localhost:9000")
                return True
            else:
                print("❌ Failed to start admin panel")
                return False
                
        except Exception as e:
            print(f"❌ Error starting admin panel: {e}")
            return False

    def run_pipeline_mode(self, mode: str, **kwargs) -> bool:
        """Run pipeline in specified mode"""
        try:
            print(f"🚀 Starting pipeline in {mode} mode...")
            
            cmd = [sys.executable, "pipeline_runner.py", "--mode", mode]
            
            # Add additional arguments
            if kwargs.get('resume'):
                cmd.append("--resume")
            if kwargs.get('batch_size'):
                cmd.extend(["--batch-size", str(kwargs['batch_size'])])
            if kwargs.get('delay'):
                cmd.extend(["--delay", str(kwargs['delay'])])
            if kwargs.get('urls'):
                cmd.extend(["--urls"] + kwargs['urls'])
            if kwargs.get('pipeline_id'):
                cmd.extend(["--pipeline-id", kwargs['pipeline_id']])
            
            process = subprocess.Popen(cmd)
            self.processes[f'pipeline_{mode}'] = process
            
            # Wait for completion
            return_code = process.wait()
            
            if return_code == 0:
                print(f"✅ Pipeline {mode} completed successfully")
                return True
            else:
                print(f"❌ Pipeline {mode} failed with code {return_code}")
                return False
                
        except Exception as e:
            print(f"❌ Error running pipeline: {e}")
            return False

    def interactive_menu(self):
        """Show interactive menu"""
        while self.running:
            print("\n" + "="*60)
            print("🚀 Enhanced MCP Pipeline - Startup Menu")
            print("="*60)
            print("1. 🌐 Start Admin Panel Only")
            print("2. 🕷️  Run Scraping Pipeline")
            print("3. 📝 Run Content Generation Pipeline")
            print("4. 🔄 Run Full Pipeline (Scrape + Generate)")
            print("5. ⏰ Setup Daily Automation")
            print("6. 📊 Check Pipeline Status")
            print("7. 🎮 Start Admin Panel + Interactive Mode")
            print("8. 🛠️  System Diagnostics")
            print("9. ❌ Exit")
            print("-"*60)
            
            try:
                choice = input("Select option (1-9): ").strip()
                
                if choice == '1':
                    self.start_admin_panel()
                    if self.processes.get('admin_panel'):
                        print("Admin panel is running. Press Ctrl+C to stop.")
                        try:
                            self.processes['admin_panel'].wait()
                        except KeyboardInterrupt:
                            pass
                
                elif choice == '2':
                    urls_input = input("Enter URLs (comma-separated) or press Enter for master data: ").strip()
                    urls = [url.strip() for url in urls_input.split(',')] if urls_input else None
                    resume = input("Resume from last checkpoint? (y/N): ").strip().lower() == 'y'
                    batch_size = input("Batch size (default 10): ").strip()
                    delay = input("Delay between requests (default 2.0): ").strip()
                    
                    kwargs = {
                        'resume': resume,
                        'batch_size': int(batch_size) if batch_size else 10,
                        'delay': float(delay) if delay else 2.0
                    }
                    if urls:
                        kwargs['urls'] = urls
                    
                    self.run_pipeline_mode('scrape', **kwargs)
                
                elif choice == '3':
                    resume = input("Resume from last checkpoint? (y/N): ").strip().lower() == 'y'
                    batch_size = input("Batch size (default 3): ").strip()
                    
                    kwargs = {
                        'resume': resume,
                        'batch_size': int(batch_size) if batch_size else 3
                    }
                    
                    self.run_pipeline_mode('generate', **kwargs)
                
                elif choice == '4':
                    urls_input = input("Enter URLs (comma-separated) or press Enter for master data: ").strip()
                    urls = [url.strip() for url in urls_input.split(',')] if urls_input else None
                    resume = input("Resume from last checkpoint? (y/N): ").strip().lower() == 'y'
                    
                    kwargs = {'resume': resume}
                    if urls:
                        kwargs['urls'] = urls
                    
                    self.run_pipeline_mode('full', **kwargs)
                
                elif choice == '5':
                    self.run_pipeline_mode('daily')
                
                elif choice == '6':
                    self.run_pipeline_mode('status')
                
                elif choice == '7':
                    # Start admin panel in background
                    if self.start_admin_panel():
                        print("\n🎮 Admin panel running in background")
                        print("🌐 Dashboard: http://localhost:9000")
                        print("📊 API Status: http://localhost:9000/api/enhanced/status")
                        print("\nYou can now use the web interface or continue with CLI options.")
                        print("Press Ctrl+C to stop everything.")
                        
                        try:
                            # Keep the admin panel running
                            while self.running and self.processes.get('admin_panel'):
                                if self.processes['admin_panel'].poll() is not None:
                                    break
                                time.sleep(1)
                        except KeyboardInterrupt:
                            pass
                
                elif choice == '8':
                    self.run_diagnostics()
                
                elif choice == '9':
                    self.running = False
                    break
                
                else:
                    print("❌ Invalid option. Please try again.")
                    
            except KeyboardInterrupt:
                self.running = False
                break
            except Exception as e:
                print(f"❌ Error: {e}")

    def run_diagnostics(self):
        """Run system diagnostics"""
        print("\n🔧 Running System Diagnostics...")
        print("-"*40)
        
        # Check dependencies
        self.check_dependencies()
        
        # Check Redis
        self.check_redis()
        
        # Check file structure
        print("📁 Checking file structure...")
        required_files = [
            'pipeline_runner.py',
            'pipeline/core/pipeline_manager.py',
            'pipeline/admin/enhanced_admin_panel.py',
            'config/pipeline_config.json'
        ]
        
        missing_files = []
        for file_path in required_files:
            if not Path(file_path).exists():
                missing_files.append(file_path)
        
        if missing_files:
            print(f"❌ Missing files: {', '.join(missing_files)}")
        else:
            print("✅ All required files present")
        
        # Check data directory
        data_dir = Path('data')
        if data_dir.exists():
            files = list(data_dir.glob('*.csv'))
            print(f"📊 Data files: {len(files)} CSV files found")
        else:
            print("⚠️  Data directory not found")
        
        # Check logs
        logs_dir = Path('logs')
        if logs_dir.exists():
            log_files = list(logs_dir.glob('*.log'))
            print(f"📝 Log files: {len(log_files)} log files found")
        else:
            print("⚠️  Logs directory not found")
        
        print("✅ Diagnostics complete")

    def quick_start(self):
        """Quick start with default settings"""
        print("🚀 Quick Start - Enhanced MCP Pipeline")
        print("="*50)
        
        # Setup
        if not self.check_dependencies():
            return False
        
        self.check_redis()
        self.setup_directories()
        
        # Start admin panel
        if self.start_admin_panel():
            print("\n✅ Quick start complete!")
            print("🌐 Admin Panel: http://localhost:9000")
            print("📊 Dashboard: http://localhost:9000")
            print("\nUse the web interface to create and manage pipelines.")
            print("Press Ctrl+C to stop.")
            
            try:
                # Keep running
                while self.running and self.processes.get('admin_panel'):
                    if self.processes['admin_panel'].poll() is not None:
                        break
                    time.sleep(1)
            except KeyboardInterrupt:
                pass
        
        return True


def main():
    """Main entry point"""
    starter = PipelineStarter()
    
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        
        if mode == 'quick':
            starter.quick_start()
        elif mode == 'admin':
            starter.check_dependencies()
            starter.setup_directories()
            starter.start_admin_panel()
            if starter.processes.get('admin_panel'):
                try:
                    starter.processes['admin_panel'].wait()
                except KeyboardInterrupt:
                    pass
        elif mode == 'diagnostics':
            starter.run_diagnostics()
        else:
            print(f"Unknown mode: {mode}")
            print("Available modes: quick, admin, diagnostics")
    else:
        # Interactive mode
        starter.check_dependencies()
        starter.setup_directories()
        starter.interactive_menu()
    
    # Cleanup
    starter._stop_all_processes()
    print("👋 Goodbye!")


if __name__ == '__main__':
    main()
